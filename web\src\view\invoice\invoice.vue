<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">
        <el-form-item label="发票状态" prop="status">
          <el-select v-model="searchInfo.status" clearable placeholder="请选择发票状态">
            <el-option
              v-for="(value, key) in INVOICE_STATUS_TEXT"
              :key="key"
              :label="value"
              :value="key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="开始日期" prop="startDate">
          <el-date-picker
            v-model="searchInfo.startDate"
            type="date"
            placeholder="选择开始日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="结束日期" prop="endDate">
          <el-date-picker
            v-model="searchInfo.endDate"
            type="date"
            placeholder="选择结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openApplyDialog">申请开票</el-button>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="ID"
      >
        <el-table-column align="left" label="申请日期" prop="CreatedAt" width="180">
          <template #default="scope">{{ formatDate(scope.row.CreatedAt) }}</template>
        </el-table-column>
        <el-table-column align="left" label="发票号码" prop="invoiceNumber" width="180">
          <template #default="scope">
            <span v-if="scope.row.invoiceNumber">{{ scope.row.invoiceNumber }}</span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="发票抬头" prop="invoiceTitle" min-width="200" />
        <el-table-column align="left" label="发票金额" prop="amount" width="120">
          <template #default="scope">
            <span class="text-red-500 font-medium">¥{{ scope.row.amount }}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="发票状态" prop="status" width="120">
          <template #default="scope">
            <el-tag :type="INVOICE_STATUS_COLOR[scope.row.status]" :icon="INVOICE_STATUS_ICON[scope.row.status]">
              {{ INVOICE_STATUS_TEXT[scope.row.status] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="left" label="开票日期" prop="issueDate" width="180">
          <template #default="scope">
            <span v-if="scope.row.issueDate">{{ formatDate(scope.row.issueDate) }}</span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="备注" prop="notes" min-width="150">
          <template #default="scope">
            <span v-if="scope.row.notes">{{ scope.row.notes }}</span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="操作" fixed="right" min-width="200">
          <template #default="scope">
            <el-button type="primary" link class="table-button" @click="getDetails(scope.row)">
              <el-icon style="margin-right: 5px"><InfoFilled /></el-icon>
              查看详情
            </el-button>
            <el-button 
              v-if="scope.row.status === INVOICE_STATUS.ISSUED && scope.row.filePath" 
              type="success" 
              link 
              class="table-button" 
              @click="downloadInvoice(scope.row)"
            >
              <el-icon style="margin-right: 5px"><Download /></el-icon>
              下载
            </el-button>
            <el-button 
              v-if="scope.row.status === INVOICE_STATUS.PENDING || scope.row.status === INVOICE_STATUS.FAILED" 
              type="danger" 
              link 
              @click="deleteRow(scope.row)"
            >
              <el-icon style="margin-right: 5px"><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 申请开票弹窗 -->
    <el-dialog
      v-model="applyDialogVisible"
      title="申请开票"
      width="600px"
      :before-close="closeApplyDialog"
    >
      <el-form :model="applyFormData" ref="applyFormRef" :rules="applyRule" label-width="100px">
        <el-form-item label="支付记录:" prop="paymentId">
          <el-select
            v-model="applyFormData.paymentId"
            placeholder="请选择支付记录"
            style="width: 100%"
            @change="onPaymentChange"
          >
            <el-option
              v-for="payment in availablePayments"
              :key="payment.id"
              :label="`${payment.meetingTitle} - ¥${payment.amount}`"
              :value="payment.id"
            />
          </el-select>
          <div class="mt-1 text-sm text-gray-500">
            选择支付记录后将自动填充发票金额
          </div>
        </el-form-item>
        <el-form-item label="开票信息:" prop="invoiceInfoId">
          <el-select
            v-model="applyFormData.invoiceInfoId"
            placeholder="请选择开票信息"
            style="width: 100%"
            @change="onInvoiceInfoChange"
          >
            <el-option
              v-for="info in invoiceInfoList"
              :key="info.ID"
              :label="`${info.invoiceTitle} (${INVOICE_TYPE_TEXT[info.invoiceType]})`"
              :value="info.ID"
            />
          </el-select>
          <div class="mt-2">
            <el-button type="text" @click="$router.push('/layout/invoice/invoiceInfo')">管理开票信息</el-button>
          </div>
          <div class="mt-1 text-sm text-gray-500">
            选择开票信息后将自动填充发票抬头和税号
          </div>
        </el-form-item>
        <el-form-item label="发票抬头:" prop="invoiceTitle">
          <el-input v-model="applyFormData.invoiceTitle" placeholder="请输入发票抬头" />
        </el-form-item>
        <el-form-item label="税号:" prop="taxNumber">
          <el-input v-model="applyFormData.taxNumber" placeholder="请输入税号（企业发票必填）" />
        </el-form-item>
        <el-form-item label="发票金额:" prop="amount">
          <el-input-number v-model="applyFormData.amount" :precision="2" :min="0" style="width: 100%" />
        </el-form-item>
        <el-form-item label="发票文件:" prop="filePath">
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            :action="uploadAction"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :limit="1"
            accept=".pdf,.jpg,.jpeg,.png"
          >
            <el-button type="primary">
              <el-icon><Upload /></el-icon>
              上传发票文件
            </el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持 PDF、JPG、PNG 格式，文件大小不超过 10MB
              </div>
            </template>
          </el-upload>
          <div v-if="applyFormData.filePath" class="mt-2">
            <el-tag type="success" closable @close="removeFile">
              <el-icon><Document /></el-icon>
              {{ getFileName(applyFormData.filePath) }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item label="备注:" prop="notes">
          <el-input v-model="applyFormData.notes" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeApplyDialog">取 消</el-button>
          <el-button type="primary" @click="submitApply">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 发票详情弹窗 -->
    <InvoiceDetail
      v-model:visible="detailDialogVisible"
      :invoice-id="selectedInvoiceId"
    />
  </div>
</template>

<script setup>
import {
  applyInvoice,
  deleteInvoice,
  getInvoiceList,
  getInvoiceInfoList,
  INVOICE_STATUS,
  INVOICE_STATUS_TEXT,
  INVOICE_STATUS_COLOR,
  INVOICE_STATUS_ICON,
  INVOICE_TYPE_TEXT
} from '@/api/invoice'

import { getAvailablePaymentsForInvoice } from '@/api/payment'

import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onMounted, computed } from 'vue'
import InvoiceDetail from './components/InvoiceDetail.vue'

defineOptions({
  name: 'Invoice'
})

const searchRule = reactive({})
const elSearchFormRef = ref()
const applyFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})

// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    pageSize.value = 10
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getInvoiceList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取详情信息
const getDetails = async (row) => {
  selectedInvoiceId.value = row.ID
  detailDialogVisible.value = true
}

// 下载发票
const downloadInvoice = (row) => {
  if (row.filePath) {
    // 这里实现下载逻辑
    window.open(row.filePath, '_blank')
  } else {
    ElMessage.warning('发票文件不存在')
  }
}

// 删除行
const deleteRow = (row) => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteInvoiceFunc(row)
  })
}

// 删除发票
const deleteInvoiceFunc = async (row) => {
  const res = await deleteInvoice({ id: row.ID })
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    if (tableData.value.length === 1 && page.value > 1) {
      page.value--
    }
    getTableData()
  }
}

// ============== 申请开票相关 ===============

const applyDialogVisible = ref(false)
const availablePayments = ref([])
const invoiceInfoList = ref([])
const uploadRef = ref()
const fileList = ref([])

// ============== 详情弹窗相关 ===============

const detailDialogVisible = ref(false)
const selectedInvoiceId = ref(null)

// ============== 文件上传相关 ===============

// 上传相关配置
const uploadAction = computed(() => {
  return `${import.meta.env.VITE_BASE_API}/fileUploadAndDownload/upload`
})

const uploadHeaders = computed(() => {
  return {
    'x-token': localStorage.getItem('token') || ''
  }
})

const applyFormData = ref({
  paymentId: null,
  invoiceInfoId: null,
  invoiceTitle: '',
  taxNumber: '',
  amount: 0,
  filePath: '',
  notes: ''
})

const applyRule = reactive({
  paymentId: [{
    required: true,
    message: '请选择支付记录',
    trigger: 'change'
  }],
  invoiceInfoId: [{
    required: true,
    message: '请选择开票信息',
    trigger: 'change'
  }],
  invoiceTitle: [{
    required: true,
    message: '请输入发票抬头',
    trigger: 'blur'
  }],
  amount: [{
    required: true,
    message: '请输入发票金额',
    trigger: 'blur'
  }]
})

// 打开申请开票弹窗
const openApplyDialog = async () => {
  // 获取可用的支付记录和开票信息
  await loadApplyData()
  applyDialogVisible.value = true
}

// 关闭申请开票弹窗
const closeApplyDialog = () => {
  applyDialogVisible.value = false
  applyFormData.value = {
    paymentId: null,
    invoiceInfoId: null,
    invoiceTitle: '',
    taxNumber: '',
    amount: 0,
    filePath: '',
    notes: ''
  }
  fileList.value = []
}

// 加载申请开票所需数据
const loadApplyData = async () => {
  // 获取用户的已支付但未开票的支付记录
  try {
    const paymentsRes = await getAvailablePaymentsForInvoice()
    if (paymentsRes.code === 0) {
      availablePayments.value = paymentsRes.data.map(payment => ({
        id: payment.ID,
        meetingTitle: payment.meeting?.title || '未知会议',
        amount: payment.amount,
        orderNumber: payment.orderNumber
      }))
    }
  } catch (error) {
    console.error('获取可开票支付记录失败:', error)
    // 如果API失败，使用模拟数据
    availablePayments.value = [
      { id: 1, meetingTitle: '2024年技术大会', amount: 500 },
      { id: 2, meetingTitle: '产品发布会', amount: 300 }
    ]
  }

  // 获取用户的开票信息
  const res = await getInvoiceInfoList({ page: 1, pageSize: 100 })
  if (res.code === 0) {
    invoiceInfoList.value = res.data.list
  }
}

// 提交申请开票
const submitApply = async () => {
  applyFormRef.value?.validate(async (valid) => {
    if (!valid) return
    
    const res = await applyInvoice(applyFormData.value)
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '申请开票成功'
      })
      closeApplyDialog()
      getTableData()
    }
  })
}

// 支付记录变更时自动填充金额
const onPaymentChange = (paymentId) => {
  const selectedPayment = availablePayments.value.find(payment => payment.id === paymentId)
  if (selectedPayment) {
    applyFormData.value.amount = selectedPayment.amount
  }
}

// 开票信息变更时自动填充发票抬头和税号
const onInvoiceInfoChange = (invoiceInfoId) => {
  const selectedInfo = invoiceInfoList.value.find(info => info.ID === invoiceInfoId)
  if (selectedInfo) {
    applyFormData.value.invoiceTitle = selectedInfo.invoiceTitle
    applyFormData.value.taxNumber = selectedInfo.taxNumber || ''
  }
}

// 文件上传前检查
const beforeUpload = (file) => {
  const isValidType = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'].includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error('只能上传 PDF、JPG、PNG 格式的文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  return true
}

// 文件上传成功
const handleUploadSuccess = (response, file) => {
  if (response.code === 0) {
    applyFormData.value.filePath = response.data.file.url
    fileList.value = [{ name: file.name, url: response.data.file.url }]
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error(response.msg || '文件上传失败')
  }
}

// 文件上传失败
const handleUploadError = (error) => {
  console.error('文件上传失败:', error)
  ElMessage.error('文件上传失败')
}

// 移除文件
const removeFile = () => {
  applyFormData.value.filePath = ''
  fileList.value = []
}

// 获取文件名
const getFileName = (filePath) => {
  if (!filePath) return ''
  return filePath.split('/').pop() || filePath.split('\\').pop() || '发票文件'
}

const formatDate = (time) => {
  if (time != null && time !== '') {
    var date = new Date(time)
    return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds()
  } else {
    return ''
  }
}

onMounted(() => {
  getTableData()
})
</script>

<style></style>
