package meeting

import (
	"errors"
	"fmt"
	"time"

	"whlxyc.cn/server/global"
	meetingModel "whlxyc.cn/server/model/meeting"
	"whlxyc.cn/server/model/meeting/request"
	"whlxyc.cn/server/model/meeting/response"
)

type PaymentService struct{}

// CreatePayment 创建支付记录
func (s *PaymentService) CreatePayment(req *request.CreatePaymentRequest, userID uint) (payment *meetingModel.Payment, err error) {
	// 检查会议注册是否存在
	var registration meetingModel.MeetingRegistration
	err = global.DY_DB.Where("id = ? AND user_id = ?", req.MeetingRegistrationID, userID).First(&registration).Error
	if err != nil {
		return nil, errors.New("会议注册记录不存在或无权限")
	}

	// 创建支付记录
	userIDInt := int(userID)
	meetingRegIDInt := int(req.MeetingRegistrationID)
	payment = &meetingModel.Payment{
		UserId:                &userIDInt,
		MeetingRegistrationId: &meetingRegIDInt,
		PaymentMethod:         &req.PaymentMethod,
		TransactionId:         &req.TransactionID,
		WechatTransactionId:   &req.WechatTransactionID,
		WechatPrepayId:        &req.WechatPrepayID,
		Amount:                &req.Amount,
		Status:                "pending",
		TransferScreenshot:    &req.TransferScreenshot,
	}

	err = global.DY_DB.Create(payment).Error
	if err != nil {
		return nil, err
	}

	// 预加载关联数据
	err = global.DY_DB.Preload("User").Preload("MeetingRegistration").First(payment, payment.ID).Error
	return payment, err
}

// UpdatePayment 更新支付记录
func (s *PaymentService) UpdatePayment(req *request.UpdatePaymentRequest) (err error) {
	// 构建查询条件
	db := global.DY_DB.Model(&meetingModel.Payment{})

	// 构建更新数据
	updateData := make(map[string]interface{})
	if req.Status != "" {
		updateData["status"] = req.Status
		// 如果状态是成功，设置支付时间
		if req.Status == meetingModel.PaymentStatusSuccess && req.PaidAt == nil {
			now := time.Now()
			updateData["paid_at"] = &now
		}
	}
	if req.WechatTransactionID != "" {
		updateData["wechat_transaction_id"] = req.WechatTransactionID
	}
	if req.WechatPrepayID != "" {
		updateData["wechat_prepay_id"] = req.WechatPrepayID
	}
	if req.PaymentData != "" {
		updateData["payment_data"] = req.PaymentData
	}
	if req.WechatCallbackData != "" {
		updateData["wechat_callback_data"] = req.WechatCallbackData
	}
	if req.TransferScreenshot != "" {
		updateData["transfer_screenshot"] = req.TransferScreenshot
	}
	if req.ReviewStatus != "" {
		updateData["review_status"] = req.ReviewStatus
	}
	if req.ReviewNotes != "" {
		updateData["review_notes"] = req.ReviewNotes
	}
	if req.PaidAt != nil {
		updateData["paid_at"] = req.PaidAt
	}
	if req.WechatPaidAt != nil {
		updateData["wechat_paid_at"] = req.WechatPaidAt
	}
	if req.FailureReason != "" {
		updateData["failure_reason"] = req.FailureReason
	}

	err = db.Where("id = ?", req.ID).Updates(updateData).Error
	return err
}

// DeletePayment 删除支付记录
func (s *PaymentService) DeletePayment(req *request.DeletePaymentRequest) (err error) {
	db := global.DY_DB

	err = db.Delete(&meetingModel.Payment{}, req.ID).Error
	return err
}

// GetPaymentByID 根据ID获取支付记录
func (s *PaymentService) GetPaymentByID(id uint, userID *uint) (payment *meetingModel.Payment, err error) {
	db := global.DY_DB.Preload("User").Preload("Meeting")
	if userID != nil {
		db = db.Where("user_id = ?", *userID)
	}
	err = db.First(&payment, id).Error
	return payment, err
}

// GetPaymentList 获取支付记录列表
func (s *PaymentService) GetPaymentList(req *request.PaymentSearch) (total int64, paymentList []meetingModel.Payment, err error) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := global.DY_DB.Model(&meetingModel.Payment{}).Preload("User").Preload("Meeting")

	// 构建查询条件
	if req.UserID != nil {
		db = db.Where("user_id = ?", *req.UserID)
	}
	if req.MeetingRegistrationID != nil {
		db = db.Where("meeting_registration_id = ?", *req.MeetingRegistrationID)
	}
	if req.Status != "" {
		db = db.Where("status = ?", req.Status)
	}
	if req.PaymentMethod != "" {
		db = db.Where("payment_method = ?", req.PaymentMethod)
	}
	if req.ReviewStatus != "" {
		db = db.Where("review_status = ?", req.ReviewStatus)
	}
	if req.TransactionID != "" {
		db = db.Where("transaction_id LIKE ?", "%"+req.TransactionID+"%")
	}
	if req.StartDate != "" {
		db = db.Where("created_at >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		db = db.Where("created_at <= ?", req.EndDate+" 23:59:59")
	}
	if req.MinAmount > 0 {
		db = db.Where("amount >= ?", req.MinAmount)
	}
	if req.MaxAmount > 0 {
		db = db.Where("amount <= ?", req.MaxAmount)
	}

	// 获取总数
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 获取列表
	err = db.Limit(limit).Offset(offset).Order("created_at DESC").Find(&paymentList).Error
	return
}

// RefundPayment 退款
func (s *PaymentService) RefundPayment(req *request.RefundPaymentRequest) (err error) {
	// 查询支付记录
	var payment meetingModel.Payment
	db := global.DY_DB

	err = db.First(&payment, req.ID).Error
	if err != nil {
		return errors.New("支付记录不存在")
	}

	// 检查支付状态
	if payment.Status != meetingModel.PaymentStatusSuccess {
		return errors.New("只有支付成功的记录才能退款")
	}

	// 检查退款金额
	amount := float64(0)
	if payment.Amount != nil {
		amount = *payment.Amount
	}
	if req.RefundAmount <= 0 || req.RefundAmount > amount {
		return errors.New("退款金额无效")
	}

	// 更新支付记录
	//now := time.Now()
	reviewNotes := ""
	if payment.ReviewNotes != nil {
		reviewNotes = *payment.ReviewNotes
	}
	newReviewNotes := reviewNotes + fmt.Sprintf(" [退款原因: %s]", req.RefundReason)

	updateData := map[string]interface{}{
		"status":       meetingModel.PaymentStatusRefunded,
		"review_notes": newReviewNotes,
	}

	err = global.DY_DB.Model(&payment).Updates(updateData).Error
	return err
}

// GetPaymentStatistics 获取支付统计
func (s *PaymentService) GetPaymentStatistics(req *request.PaymentStatisticsRequest) (statistics *response.PaymentStatisticsResponse, err error) {
	db := global.DY_DB.Model(&meetingModel.Payment{})

	// 构建查询条件

	if req.MeetingRegistrationID != nil {
		db = db.Where("meeting_registration_id = ?", *req.MeetingRegistrationID)
	}
	if req.StartDate != "" {
		db = db.Where("created_at >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		db = db.Where("created_at <= ?", req.EndDate+" 23:59:59")
	}

	statistics = &response.PaymentStatisticsResponse{}

	// 总体统计
	db.Count(&statistics.TotalPayments)
	db.Select("COALESCE(SUM(amount), 0)").Scan(&statistics.TotalAmount)

	// 按状态统计
	db.Where("status = ?", meetingModel.PaymentStatusSuccess).Count(&statistics.SuccessPayments)
	db.Where("status = ?", meetingModel.PaymentStatusSuccess).Select("COALESCE(SUM(amount), 0)").Scan(&statistics.SuccessAmount)

	db.Where("status = ?", meetingModel.PaymentStatusPending).Count(&statistics.PendingPayments)
	db.Where("status = ?", meetingModel.PaymentStatusPending).Select("COALESCE(SUM(amount), 0)").Scan(&statistics.PendingAmount)

	db.Where("status = ?", meetingModel.PaymentStatusRefunded).Count(&statistics.RefundedPayments)
	db.Where("status = ?", meetingModel.PaymentStatusRefunded).Select("COALESCE(SUM(refund_amount), 0)").Scan(&statistics.RefundedAmount)

	db.Where("status = ?", meetingModel.PaymentStatusFailed).Count(&statistics.FailedPayments)
	db.Where("status = ?", meetingModel.PaymentStatusFailed).Select("COALESCE(SUM(amount), 0)").Scan(&statistics.FailedAmount)

	return statistics, nil
}

// GetAvailablePaymentsForInvoice 获取可用于开票的支付记录
func (s *PaymentService) GetAvailablePaymentsForInvoice(userID uint) (payments []meetingModel.Payment, err error) {
	// 查询已支付但未开票的支付记录
	err = global.DY_DB.Preload("MeetingRegistration").
		Where("user_id = ? AND status = ?", userID, meetingModel.PaymentStatusSuccess).
		Where("id NOT IN (SELECT payment_id FROM invoices WHERE user_id = ?)", userID).
		Order("created_at DESC").
		Find(&payments).Error
	return payments, err
}
