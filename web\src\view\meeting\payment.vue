<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">
        <el-form-item label="支付状态" prop="status">
          <el-select v-model="searchInfo.status" clearable placeholder="请选择支付状态">
            <el-option
              v-for="(value, key) in PAYMENT_STATUS_TEXT"
              :key="key"
              :label="value"
              :value="key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="支付方式" prop="paymentMethod">
          <el-select v-model="searchInfo.paymentMethod" clearable placeholder="请选择支付方式">
            <el-option
              v-for="(value, key) in PAYMENT_METHOD_TEXT"
              :key="key"
              :label="value"
              :value="key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="订单号" prop="orderNumber">
          <el-input v-model="searchInfo.orderNumber" clearable placeholder="请输入订单号" />
        </el-form-item>
        <el-form-item label="开始日期" prop="startDate">
          <el-date-picker
            v-model="searchInfo.startDate"
            type="date"
            placeholder="选择开始日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="结束日期" prop="endDate">
          <el-date-picker
            v-model="searchInfo.endDate"
            type="date"
            placeholder="选择结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 统计卡片 -->
    <div class="gva-card-box mb-4">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-content">
              <div class="statistics-icon pending">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="statistics-info">
                <div class="statistics-number">{{ statistics.pendingPayments }}</div>
                <div class="statistics-label">待支付</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-content">
              <div class="statistics-icon success">
                <el-icon><Check /></el-icon>
              </div>
              <div class="statistics-info">
                <div class="statistics-number">{{ statistics.successPayments }}</div>
                <div class="statistics-label">支付成功</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-content">
              <div class="statistics-icon failed">
                <el-icon><Close /></el-icon>
              </div>
              <div class="statistics-info">
                <div class="statistics-number">{{ statistics.failedPayments }}</div>
                <div class="statistics-label">支付失败</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-content">
              <div class="statistics-icon refunded">
                <el-icon><RefreshLeft /></el-icon>
              </div>
              <div class="statistics-info">
                <div class="statistics-number">{{ statistics.refundedPayments }}</div>
                <div class="statistics-label">已退款</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openDialog">新增支付记录</el-button>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="ID"
      >
        <el-table-column align="left" label="创建日期" prop="CreatedAt" width="180">
          <template #default="scope">{{ formatDate(scope.row.CreatedAt) }}</template>
        </el-table-column>
        <el-table-column align="left" label="订单号" prop="orderNumber" width="180" />
        <el-table-column align="left" label="用户" prop="user" width="120">
          <template #default="scope">
            <div v-if="scope.row.user">
              <div>{{ scope.row.user.nickName || scope.row.user.username }}</div>
              <div class="text-xs text-gray-500">ID: {{ scope.row.user.ID }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="left" label="会议" prop="meeting" min-width="200">
          <template #default="scope">
            <div v-if="scope.row.meeting">
              {{ scope.row.meeting.title }}
            </div>
          </template>
        </el-table-column>
        <el-table-column align="left" label="支付金额" prop="amount" width="120">
          <template #default="scope">
            <span class="text-red-500 font-medium">¥{{ scope.row.amount }}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="支付方式" prop="paymentMethod" width="120">
          <template #default="scope">
            <el-tag :type="PAYMENT_METHOD_COLOR[scope.row.paymentMethod]">
              {{ PAYMENT_METHOD_TEXT[scope.row.paymentMethod] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="left" label="支付状态" prop="status" width="120">
          <template #default="scope">
            <el-tag 
              :type="PAYMENT_STATUS_COLOR[scope.row.status]" 
              :icon="PAYMENT_STATUS_ICON[scope.row.status]"
            >
              {{ PAYMENT_STATUS_TEXT[scope.row.status] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="left" label="支付时间" prop="paidAt" width="180">
          <template #default="scope">
            <span v-if="scope.row.paidAt">{{ formatDate(scope.row.paidAt) }}</span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="操作" fixed="right" min-width="240">
          <template #default="scope">
            <el-button type="primary" link class="table-button" @click="getDetails(scope.row)">
              <el-icon style="margin-right: 5px"><InfoFilled /></el-icon>
              查看详情
            </el-button>
            <el-button type="warning" link class="table-button" @click="updatePaymentFunc(scope.row)">
              <el-icon style="margin-right: 5px"><Edit /></el-icon>
              编辑
            </el-button>
            <el-button 
              v-if="scope.row.status === PAYMENT_STATUS.SUCCESS" 
              type="info" 
              link 
              class="table-button" 
              @click="refundPaymentFunc(scope.row)"
            >
              <el-icon style="margin-right: 5px"><RefreshLeft /></el-icon>
              退款
            </el-button>
            <el-button type="danger" link @click="deleteRow(scope.row)">
              <el-icon style="margin-right: 5px"><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    
    <!-- 支付记录表单弹窗 -->
    <el-drawer
      destroy-on-close
      size="800"
      v-model="dialogFormVisible"
      :show-close="false"
      :before-close="closeDialog"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg">{{ type === 'create' ? '添加' : '修改' }}支付记录</span>
          <div>
            <el-button @click="closeDialog">取 消</el-button>
            <el-button type="primary" @click="enterDialog">确 定</el-button>
          </div>
        </div>
      </template>

      <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
        <el-form-item label="会议:" prop="meetingId">
          <el-select v-model="formData.meetingId" placeholder="请选择会议" style="width: 100%">
            <el-option
              v-for="meeting in meetingList"
              :key="meeting.ID"
              :label="meeting.title"
              :value="meeting.ID"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="支付金额:" prop="amount">
          <el-input-number v-model="formData.amount" :precision="2" :min="0" style="width: 100%" />
        </el-form-item>
        <el-form-item label="支付方式:" prop="paymentMethod">
          <el-radio-group v-model="formData.paymentMethod">
            <el-radio :value="PAYMENT_METHOD.ALIPAY">{{ PAYMENT_METHOD_TEXT[PAYMENT_METHOD.ALIPAY] }}</el-radio>
            <el-radio :value="PAYMENT_METHOD.WECHAT">{{ PAYMENT_METHOD_TEXT[PAYMENT_METHOD.WECHAT] }}</el-radio>
            <el-radio :value="PAYMENT_METHOD.BANK">{{ PAYMENT_METHOD_TEXT[PAYMENT_METHOD.BANK] }}</el-radio>
            <el-radio :value="PAYMENT_METHOD.CASH">{{ PAYMENT_METHOD_TEXT[PAYMENT_METHOD.CASH] }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="type === 'update'" label="支付状态:" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :value="PAYMENT_STATUS.PENDING">{{ PAYMENT_STATUS_TEXT[PAYMENT_STATUS.PENDING] }}</el-radio>
            <el-radio :value="PAYMENT_STATUS.SUCCESS">{{ PAYMENT_STATUS_TEXT[PAYMENT_STATUS.SUCCESS] }}</el-radio>
            <el-radio :value="PAYMENT_STATUS.FAILED">{{ PAYMENT_STATUS_TEXT[PAYMENT_STATUS.FAILED] }}</el-radio>
            <el-radio :value="PAYMENT_STATUS.REFUNDED">{{ PAYMENT_STATUS_TEXT[PAYMENT_STATUS.REFUNDED] }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="第三方交易ID:" prop="transactionId">
          <el-input v-model="formData.transactionId" clearable placeholder="请输入第三方交易ID" />
        </el-form-item>
        <el-form-item label="备注:" prop="notes">
          <el-input v-model="formData.notes" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
    </el-drawer>
  </div>
</template>

<script setup>
import {
  createPayment,
  deletePayment,
  updatePayment,
  getPaymentById,
  getPaymentList,
  getPaymentStatistics,
  refundPayment,
  PAYMENT_METHOD,
  PAYMENT_STATUS,
  PAYMENT_METHOD_TEXT,
  PAYMENT_STATUS_TEXT,
  PAYMENT_METHOD_COLOR,
  PAYMENT_STATUS_COLOR,
  PAYMENT_STATUS_ICON
} from '@/api/payment'

import { getMeetingList } from '@/api/meeting'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onMounted } from 'vue'

defineOptions({
  name: 'Payment'
})

const methodMap = {
  create: createPayment,
  update: updatePayment
}

const searchRule = reactive({})
const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})
const statistics = ref({
  totalPayments: 0,
  pendingPayments: 0,
  successPayments: 0,
  failedPayments: 0,
  refundedPayments: 0
})

// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    pageSize.value = 10
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getPaymentList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

// 获取统计数据
const getStatistics = async() => {
  const res = await getPaymentStatistics()
  if (res.code === 0) {
    statistics.value = res.data
  }
}

getTableData()
getStatistics()

// ============== 表格控制部分结束 ===============

// 获取详情信息
const getDetails = async (row) => {
  ElMessage.info('查看详情功能')
}

// 删除行
const deleteRow = (row) => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deletePaymentFunc(row)
  })
}

// 删除支付记录
const deletePaymentFunc = async (row) => {
  const res = await deletePayment({ id: row.ID })
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    if (tableData.value.length === 1 && page.value > 1) {
      page.value--
    }
    getTableData()
    getStatistics()
  }
}

// 退款
const refundPaymentFunc = (row) => {
  ElMessageBox.prompt('请输入退款原因', '退款确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /.+/,
    inputErrorMessage: '退款原因不能为空'
  }).then(({ value }) => {
    refundPayment({
      id: row.ID,
      refundAmount: row.amount,
      refundReason: value
    }).then(res => {
      if (res.code === 0) {
        ElMessage.success('退款成功')
        getTableData()
        getStatistics()
      }
    })
  })
}

// 弹窗控制标记
const dialogFormVisible = ref(false)
const meetingList = ref([])

// 打开弹窗
const openDialog = () => {
  type.value = 'create'
  dialogFormVisible.value = true
  loadMeetingList()
}

// 关闭弹窗
const closeDialog = () => {
  dialogFormVisible.value = false
  formData.value = {
    meetingId: null,
    amount: 0,
    paymentMethod: PAYMENT_METHOD.ALIPAY,
    status: PAYMENT_STATUS.PENDING,
    transactionId: '',
    notes: ''
  }
}

// 弹窗确定
const enterDialog = async () => {
  elFormRef.value?.validate( async (valid) => {
    if (!valid) return
    let res
    switch (type.value) {
      case 'create':
        res = await createPayment(formData.value)
        break
      case 'update':
        res = await updatePayment(formData.value)
        break
      default:
        res = await createPayment(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '创建/更改成功'
      })
      closeDialog()
      getTableData()
      getStatistics()
    }
  })
}

const updatePaymentFunc = async (row) => {
  const res = await getPaymentById(row.ID)
  type.value = 'update'
  if (res.code === 0) {
    formData.value = res.data
    dialogFormVisible.value = true
    loadMeetingList()
  }
}

// 加载会议列表
const loadMeetingList = async () => {
  const res = await getMeetingList({ page: 1, pageSize: 100 })
  if (res.code === 0) {
    meetingList.value = res.data.list || []
  }
}

const type = ref('')
const formData = ref({
  meetingId: null,
  amount: 0,
  paymentMethod: PAYMENT_METHOD.ALIPAY,
  status: PAYMENT_STATUS.PENDING,
  transactionId: '',
  notes: ''
})

// 验证规则
const rule = reactive({
  meetingId: [{
    required: true,
    message: '请选择会议',
    trigger: 'change'
  }],
  amount: [{
    required: true,
    message: '请输入支付金额',
    trigger: 'blur'
  }],
  paymentMethod: [{
    required: true,
    message: '请选择支付方式',
    trigger: 'change'
  }]
})

const formatDate = (time) => {
  if (time != null && time !== '') {
    var date = new Date(time)
    return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds()
  } else {
    return ''
  }
}

onMounted(() => {
  getTableData()
  getStatistics()
})
</script>

<style scoped>
.statistics-card {
  cursor: pointer;
  transition: all 0.3s;
}

.statistics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.statistics-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.statistics-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.statistics-icon.pending {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.statistics-icon.success {
  background: linear-gradient(135deg, #27ae60, #229954);
}

.statistics-icon.failed {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.statistics-icon.refunded {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.statistics-info {
  flex: 1;
}

.statistics-number {
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
  line-height: 1;
}

.statistics-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 5px;
}
</style>
