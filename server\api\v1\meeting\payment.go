package meeting

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"whlxyc.cn/server/global"
	"whlxyc.cn/server/model/common/response"
	"whlxyc.cn/server/model/meeting/request"
	"whlxyc.cn/server/service"
	"whlxyc.cn/server/utils"
)

type PaymentApi struct{}

// CreatePayment 创建支付记录
// @Tags Payment
// @Summary 创建支付记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.CreatePaymentRequest true "创建支付记录"
// @Success 200 {object} response.Response{data=meeting.Payment} "创建成功"
// @Router /payment/createPayment [post]
func (p *PaymentApi) CreatePayment(c *gin.Context) {
	var req request.CreatePaymentRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	payment, err := service.ServiceGroupApp.MeetingServiceGroup.PaymentService.CreatePayment(&req, userID)
	if err != nil {
		global.DY_LOG.Error("创建支付记录失败!", zap.Error(err))
		response.FailWithMessage("创建失败: "+err.Error(), c)
		return
	}

	response.OkWithData(payment, c)
}

// UpdatePayment 更新支付记录
// @Tags Payment
// @Summary 更新支付记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.UpdatePaymentRequest true "更新支付记录"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /payment/updatePayment [put]
func (p *PaymentApi) UpdatePayment(c *gin.Context) {
	var req request.UpdatePaymentRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = service.ServiceGroupApp.MeetingServiceGroup.PaymentService.UpdatePayment(&req)
	if err != nil {
		global.DY_LOG.Error("更新支付记录失败!", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// AdminUpdatePayment 管理员更新支付记录
// @Tags Payment
// @Summary 管理员更新支付记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.UpdatePaymentRequest true "更新支付记录"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /payment/adminUpdatePayment [put]
func (p *PaymentApi) AdminUpdatePayment(c *gin.Context) {
	var req request.UpdatePaymentRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = service.ServiceGroupApp.MeetingServiceGroup.PaymentService.UpdatePayment(&req)
	if err != nil {
		global.DY_LOG.Error("管理员更新支付记录失败!", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// DeletePayment 删除支付记录
// @Tags Payment
// @Summary 删除支付记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.DeletePaymentRequest true "删除支付记录"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /payment/deletePayment [delete]
func (p *PaymentApi) DeletePayment(c *gin.Context) {
	var req request.DeletePaymentRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = service.ServiceGroupApp.MeetingServiceGroup.PaymentService.DeletePayment(&req)
	if err != nil {
		global.DY_LOG.Error("删除支付记录失败!", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// AdminDeletePayment 管理员删除支付记录
// @Tags Payment
// @Summary 管理员删除支付记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.DeletePaymentRequest true "删除支付记录"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /payment/adminDeletePayment [delete]
func (p *PaymentApi) AdminDeletePayment(c *gin.Context) {
	var req request.DeletePaymentRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = service.ServiceGroupApp.MeetingServiceGroup.PaymentService.DeletePayment(&req)
	if err != nil {
		global.DY_LOG.Error("管理员删除支付记录失败!", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// GetPaymentByID 根据ID获取支付记录
// @Tags Payment
// @Summary 根据ID获取支付记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id query uint true "支付记录ID"
// @Success 200 {object} response.Response{data=meeting.Payment} "获取成功"
// @Router /payment/findPayment [get]
func (p *PaymentApi) GetPaymentByID(c *gin.Context) {
	var req struct {
		ID uint `form:"id" binding:"required"`
	}
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	payment, err := service.ServiceGroupApp.MeetingServiceGroup.PaymentService.GetPaymentByID(req.ID, &userID)
	if err != nil {
		global.DY_LOG.Error("查询支付记录失败!", zap.Error(err))
		response.FailWithMessage("查询失败: "+err.Error(), c)
		return
	}

	response.OkWithData(payment, c)
}

// AdminGetPaymentByID 管理员根据ID获取支付记录
// @Tags Payment
// @Summary 管理员根据ID获取支付记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id query uint true "支付记录ID"
// @Success 200 {object} response.Response{data=meeting.Payment} "获取成功"
// @Router /payment/adminFindPayment [get]
func (p *PaymentApi) AdminGetPaymentByID(c *gin.Context) {
	var req struct {
		ID uint `form:"id" binding:"required"`
	}
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	payment, err := service.ServiceGroupApp.MeetingServiceGroup.PaymentService.GetPaymentByID(req.ID, nil)
	if err != nil {
		global.DY_LOG.Error("管理员查询支付记录失败!", zap.Error(err))
		response.FailWithMessage("查询失败: "+err.Error(), c)
		return
	}

	response.OkWithData(payment, c)
}

// GetPaymentList 获取支付记录列表
// @Tags Payment
// @Summary 获取支付记录列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PaymentSearch true "获取支付记录列表"
// @Success 200 {object} response.Response{data=response.PageResult} "获取成功"
// @Router /payment/getPaymentList [get]
func (p *PaymentApi) GetPaymentList(c *gin.Context) {
	var req request.PaymentSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	total, paymentList, err := service.ServiceGroupApp.MeetingServiceGroup.PaymentService.GetPaymentList(&req)
	if err != nil {
		global.DY_LOG.Error("获取支付记录列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     paymentList,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// AdminGetPaymentList 管理员获取支付记录列表
// @Tags Payment
// @Summary 管理员获取支付记录列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PaymentSearch true "获取支付记录列表"
// @Success 200 {object} response.Response{data=response.PageResult} "获取成功"
// @Router /payment/adminGetPaymentList [get]
func (p *PaymentApi) AdminGetPaymentList(c *gin.Context) {
	var req request.PaymentSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	total, paymentList, err := service.ServiceGroupApp.MeetingServiceGroup.PaymentService.GetPaymentList(&req)
	if err != nil {
		global.DY_LOG.Error("管理员获取支付记录列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     paymentList,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// RefundPayment 退款
// @Tags Payment
// @Summary 退款
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.RefundPaymentRequest true "退款"
// @Success 200 {object} response.Response{msg=string} "退款成功"
// @Router /payment/refundPayment [post]
func (p *PaymentApi) RefundPayment(c *gin.Context) {
	var req request.RefundPaymentRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = service.ServiceGroupApp.MeetingServiceGroup.PaymentService.RefundPayment(&req)
	if err != nil {
		global.DY_LOG.Error("退款失败!", zap.Error(err))
		response.FailWithMessage("退款失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("退款成功", c)
}

// AdminRefundPayment 管理员退款
// @Tags Payment
// @Summary 管理员退款
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.RefundPaymentRequest true "退款"
// @Success 200 {object} response.Response{msg=string} "退款成功"
// @Router /payment/adminRefundPayment [post]
func (p *PaymentApi) AdminRefundPayment(c *gin.Context) {
	var req request.RefundPaymentRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = service.ServiceGroupApp.MeetingServiceGroup.PaymentService.RefundPayment(&req)
	if err != nil {
		global.DY_LOG.Error("管理员退款失败!", zap.Error(err))
		response.FailWithMessage("退款失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("退款成功", c)
}

// GetPaymentStatistics 获取支付统计
// @Tags Payment
// @Summary 获取支付统计
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PaymentStatisticsRequest true "获取支付统计"
// @Success 200 {object} response.Response{data=response.PaymentStatisticsResponse} "获取成功"
// @Router /payment/getPaymentStatistics [get]
func (p *PaymentApi) GetPaymentStatistics(c *gin.Context) {
	var req request.PaymentStatisticsRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	statistics, err := service.ServiceGroupApp.MeetingServiceGroup.PaymentService.GetPaymentStatistics(&req)
	if err != nil {
		global.DY_LOG.Error("获取支付统计失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(statistics, c)
}

// AdminGetPaymentStatistics 管理员获取支付统计
// @Tags Payment
// @Summary 管理员获取支付统计
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PaymentStatisticsRequest true "获取支付统计"
// @Success 200 {object} response.Response{data=response.PaymentStatisticsResponse} "获取成功"
// @Router /payment/adminGetPaymentStatistics [get]
func (p *PaymentApi) AdminGetPaymentStatistics(c *gin.Context) {
	var req request.PaymentStatisticsRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	statistics, err := service.ServiceGroupApp.MeetingServiceGroup.PaymentService.GetPaymentStatistics(&req)
	if err != nil {
		global.DY_LOG.Error("管理员获取支付统计失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(statistics, c)
}

// GetAvailablePaymentsForInvoice 获取可用于开票的支付记录
// @Tags Payment
// @Summary 获取可用于开票的支付记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]meeting.Payment} "获取成功"
// @Router /payment/getAvailablePaymentsForInvoice [get]
func (p *PaymentApi) GetAvailablePaymentsForInvoice(c *gin.Context) {
	// 获取当前用户ID
	userID := utils.GetUserID(c)

	payments, err := service.ServiceGroupApp.MeetingServiceGroup.PaymentService.GetAvailablePaymentsForInvoice(userID)
	if err != nil {
		global.DY_LOG.Error("获取可开票支付记录失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(payments, c)
}
